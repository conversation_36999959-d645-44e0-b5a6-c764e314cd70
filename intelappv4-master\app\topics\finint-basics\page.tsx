const FinintBasicsPage = () => {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-4">Financial Intelligence Basics</h1>

      <section className="mb-6">
        <h2>What is FININT?</h2>
        <p className="italic">
          &quot;Following the money trail, even when it takes a scenic route through seventeen shell companies in tax
          havens.&quot;
        </p>
        <p>
          FININT, or Financial Intelligence, is the practice of gathering and analyzing financial information to combat
          financial crimes such as money laundering, terrorist financing, and fraud. It involves identifying suspicious
          financial activities and tracing the flow of funds to uncover illicit activities.
        </p>
      </section>

      <section className="mb-6">
        <h2>Key Components of FININT</h2>
        <p className="italic">
          &quot;Spreadsheets, suspicious transactions, and the eternal quest to make Excel do things it was never meant to
          do.&quot;
        </p>
        <ul>
          <li>
            <b>Data Collection:</b> Gathering financial data from various sources, including banks, credit card
            companies, and government agencies.
          </li>
          <li>
            <b>Data Analysis:</b> Analyzing financial data to identify patterns, anomalies, and suspicious transactions.
          </li>
          <li>
            <b>Reporting:</b> Reporting suspicious activities to law enforcement and regulatory agencies.
          </li>
          <li>
            <b>Investigation:</b> Investigating potential financial crimes based on the analysis of financial data.
          </li>
        </ul>
      </section>

      <section className="mb-6">
        <h2>FININT in Practice</h2>
        <p className="italic">
          &quot;Where &apos;show me the money&apos; becomes &apos;show me where the money went, who touched it, and why it&apos;s now in
          cryptocurrency.&apos;&quot;
        </p>
        <p>
          In practice, FININT involves using various tools and techniques to analyze financial data, such as data
          mining, statistical analysis, and network analysis. It also involves collaborating with law enforcement and
          regulatory agencies to investigate and prosecute financial crimes.
        </p>
      </section>

      <section className="mb-6">
        <h2>Career in FININT</h2>
        <p className="italic">
          &quot;Professional money detective with an unhealthy obsession with bank statements and a love-hate relationship
          with pivot tables.&quot;
        </p>
        <p>
          A career in FININT can be rewarding for those who are interested in combating financial crime and have strong
          analytical skills. Common roles include financial analysts, investigators, and compliance officers.
        </p>
      </section>

      <section className="mb-6">
        <h2>Advanced FININT Concepts</h2>
        <p className="italic">
          &quot;When basic financial tracking isn&apos;t enough, and you need to follow digital breadcrumbs through the financial
          matrix.&quot;
        </p>
        <p>
          Advanced FININT concepts include the use of artificial intelligence and machine learning to detect and prevent
          financial crimes, as well as the analysis of complex financial networks and the use of blockchain technology
          to trace the flow of funds.
        </p>
      </section>
    </div>
  )
}

export default FinintBasicsPage
