import type React from "react"

const IntelligenceFieldGuide: React.FC = () => {
  return (
    <div>
      <h1>🎭 Intelligence Discipline Humor:</h1>
      <div>
        <h2>🔍 OSINT (Open Source Intelligence)</h2>
        <ul>
          <li>
            <strong>Stereotypes:</strong> "Professional internet stalkers with a badge"
          </li>
          <li>
            <strong>Daily Reality:</strong> "Spent 6 hours on social media... for work!"
          </li>
          <li>
            <strong>Tools:</strong> "Google is my best friend, Wikipedia is my frenemy"
          </li>
          <li>
            <strong>Challenges:</strong> "When your browser history looks suspicious but it's all work"
          </li>
        </ul>
      </div>
      <div>
        <h2>👥 HUMINT (Human Intelligence)</h2>
        <ul>
          <li>
            <strong>Stereotypes:</strong> "The people person who remembers everyone's birthday"
          </li>
          <li>
            <strong>Daily Reality:</strong> "Building rapport over coffee... again"
          </li>
          <li>
            <strong>Skills:</strong> "Can make friends with a brick wall"
          </li>
          <li>
            <strong>Challenges:</strong> "When your source wants to be best friends on Facebook"
          </li>
        </ul>
      </div>
      <div>
        <h2>📡 SIGINT (Signals Intelligence)</h2>
        <ul>
          <li>
            <strong>Stereotypes:</strong> "The tech wizard who speaks in acronyms"
          </li>
          <li>
            <strong>Daily Reality:</strong> "Listening to static and calling it music"
          </li>
          <li>
            <strong>Tools:</strong> "Has more antennas than a NASA facility"
          </li>
          <li>
            <strong>Challenges:</strong> "When the most interesting conversation is encrypted"
          </li>
        </ul>
      </div>
      <div>
        <h2>🛰️ GEOINT (Geospatial Intelligence)</h2>
        <ul>
          <li>
            <strong>Stereotypes:</strong> "Can spot a needle in a haystack from space"
          </li>
          <li>
            <strong>Daily Reality:</strong> "Zooming and enhancing like CSI (but it actually works)"
          </li>
          <li>
            <strong>Skills:</strong> "Knows every building in the city from above"
          </li>
          <li>
            <strong>Challenges:</strong> "When clouds ruin your perfect satellite shot"
          </li>
        </ul>
      </div>
      <div>
        <h2>🔬 MASINT (Measurement and Signature Intelligence)</h2>
        <ul>
          <li>
            <strong>Stereotypes:</strong> "The science nerd who measures everything"
          </li>
          <li>
            <strong>Daily Reality:</strong> "Analyzing signatures that aren't autographs"
          </li>
          <li>
            <strong>Tools:</strong> "Sensors for things you didn't know existed"
          </li>
          <li>
            <strong>Challenges:</strong> "Explaining what you do at parties"
          </li>
        </ul>
      </div>
      <div>
        <h2>💰 FININT (Financial Intelligence)</h2>
        <ul>
          <li>
            <strong>Stereotypes:</strong> "Follows the money like a bloodhound"
          </li>
          <li>
            <strong>Daily Reality:</strong> "Making Excel spreadsheets look exciting"
          </li>
          <li>
            <strong>Skills:</strong> "Can trace a dollar through 47 shell companies"
          </li>
          <li>
            <strong>Challenges:</strong> "When offshore accounts have more layers than an onion"
          </li>
        </ul>
      </div>
      <h1>🎯 Humor Features:</h1>
      <div>
        <h2>1. Discipline-Specific Memes</h2>
        <p>Interactive meme generator with intelligence themes</p>
        <p>Classic formats adapted for intel work</p>
        <p>Shareable content for training sessions</p>
      </div>
      <div>
        <h2>2. Jargon Translator</h2>
        <p>Funny "translations" of intelligence acronyms</p>
        <p>What analysts really mean vs. what they say</p>
        <p>Bureaucratic speak decoder</p>
      </div>
      <div>
        <h2>3. Field Guide Cards</h2>
        <p>"How to spot" different types of analysts</p>
        <p>Natural habitat descriptions</p>
        <p>Feeding and care instructions (coffee requirements)</p>
      </div>
      <div>
        <h2>4. Daily Life Humor</h2>
        <p>"A day in the life" scenarios for each discipline</p>
        <p>Common frustrations turned into comedy</p>
        <p>Relatable workplace situations</p>
      </div>
      <h1>📚 Educational Value:</h1>
      <div>
        <h2>Memorable Learning</h2>
        <p>Humor helps students remember key concepts</p>
        <p>Funny analogies make complex topics accessible</p>
        <p>Reduces intimidation factor of technical subjects</p>
      </div>
      <div>
        <h2>Professional Relatability</h2>
        <p>Based on real analyst experiences</p>
        <p>Acknowledges common challenges with humor</p>
        <p>Builds community through shared experiences</p>
      </div>
      <div>
        <h2>Engagement Boost</h2>
        <p>Interactive elements keep students interested</p>
        <p>Shareable content extends learning beyond classroom</p>
        <p>Makes serious topics approachable</p>
      </div>
      <p>The humor is designed to be:</p>
      <ul>
        <li>
          <strong>Professional</strong> but not stuffy
        </li>
        <li>
          <strong>Educational</strong> while entertaining
        </li>
        <li>
          <strong>Inclusive</strong> and respectful
        </li>
        <li>
          <strong>Memorable</strong> and engaging
        </li>
      </ul>
      <p>
        This creates a learning environment where students can laugh while learning, making complex intelligence
        concepts more accessible and enjoyable!
      </p>
    </div>
  )
}

export default IntelligenceFieldGuide
