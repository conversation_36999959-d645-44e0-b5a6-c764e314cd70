"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"
import { cn } from "@/lib/utils"

const NeumorphicProgress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>
>(({ className, value, ...props }, ref) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn("neu-progress relative h-4 w-full overflow-hidden", className)}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className="neu-progress-bar h-full w-full flex-1 transition-all"
      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
    />
  </ProgressPrimitive.Root>
))
NeumorphicProgress.displayName = ProgressPrimitive.Root.displayName

export { NeumorphicProgress }
