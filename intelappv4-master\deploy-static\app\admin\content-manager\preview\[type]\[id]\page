import { Suspense } from "react"
import { notFound } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardT<PERSON>le, CardFooter } from "@/components/ui/card"
import { ArrowLeft, Edit } from "lucide-react"
import Link from "next/link"
import ContentRenderer from "@/components/content-renderer"

interface PreviewPageProps {
  params: {
    type: string
    id: string
  }
}

export default function ContentPreviewPage({ params }: PreviewPageProps) {
  const { type, id } = params

  // Validate page type
  const validTypes = ["topic", "learning-path", "advanced-topic", "other"]
  if (!validTypes.includes(type)) {
    notFound()
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <Link href="/admin/content-manager">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Content Manager
          </Button>
        </Link>
        <h1 className="text-3xl font-bold ml-4">Content Preview</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <span>
              {formatPageType(type)}: {formatPageId(id)}
            </span>
            <Link href={`/admin/content-manager?type=${type}&id=${id}`}>
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit Content
              </Button>
            </Link>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border rounded-md p-6 bg-white">
            <Suspense fallback={<div>Loading content...</div>}>
              <ContentRenderer pageType={type} pageId={id} />
            </Suspense>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Link href="/admin/content-manager">
            <Button variant="outline">Back to Content Manager</Button>
          </Link>
          <Link href={`/admin/content-manager?type=${type}&id=${id}`}>
            <Button>Edit Content</Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  )
}

// Helper functions
function formatPageType(type: string): string {
  const typeMap: Record<string, string> = {
    topic: "Topic Page",
    "learning-path": "Learning Path",
    "advanced-topic": "Advanced Topic",
    other: "Other Page",
  }

  return typeMap[type] || type
}

function formatPageId(id: string): string {
  return id
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ")
}
