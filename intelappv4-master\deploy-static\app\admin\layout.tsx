import type React from "react"
import type { Metada<PERSON> } from "next"
import AdminLayoutClient from "./AdminLayoutClient"

export const metadata: Metadata = {
  title: "Intel Analyst Academy - Admin",
  description: "Admin dashboard for Intel Analyst Academy",
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <AdminLayoutClient>{children}</AdminLayoutClient>
}
