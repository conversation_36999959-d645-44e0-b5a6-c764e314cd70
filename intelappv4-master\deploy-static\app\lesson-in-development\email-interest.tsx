"use client"

import { But<PERSON> } from "@/components/ui/button"

interface EmailInterestProps {
  subject: string
  body: string
  children: React.ReactNode
}

export default function EmailInterest({ subject, body, children }: EmailInterestProps) {
  const email = "<EMAIL>"

  return (
    <Button asChild>
      <a href={`mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`}>
        {children}
      </a>
    </Button>
  )
}