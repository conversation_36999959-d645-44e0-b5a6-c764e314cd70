import Link from "next/link"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, FileText, CheckCircle } from "lucide-react"
import type { Metada<PERSON> } from "next"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Card, CardContent } from "@/components/ui/card"
import { StaticImage } from "@/components/static-image"
import { WhatsNext } from "@/components/whats-next"

export const metadata: Metadata = {
  title: "Multi-Source Integration | The Intel Analyst Academy",
  description: "Learn how to integrate information from multiple intelligence sources to create a more complete picture.",
}

export default function MultiSourceIntegrationPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/topics">Topics</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/learning-paths/report-writing">Report Writing</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/topics/multi-source-integration" isCurrentPage>
              Multi-Source Integration
            </BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="mb-8">
        <Link
          href="/learning-paths/report-writing"
          className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 mb-4"
        >
          <ArrowLeft className="mr-1 h-4 w-4" />
          Back to Report Writing
        </Link>
        <h1 className="text-4xl font-bold tracking-tight mb-4">Multi-Source Integration</h1>
        <div className="flex items-center text-muted-foreground mb-6">
          <Clock className="mr-2 h-4 w-4" />
          <span>30 min read</span>
          <FileText className="ml-6 mr-2 h-4 w-4" />
          <span>Core Topic</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
        <div className="lg:col-span-2">
          <div className="prose prose-lg max-w-none">
            <div className="relative w-full h-64 mb-8 rounded-lg overflow-hidden">
              <StaticImage
                src="/multi-source-integration-banner.png"
                alt="Multi-Source Integration"
                fill
                className="object-cover"
              />
            </div>

            <h2>The Fusion of Intelligence</h2>
            <p>
              Rarely does a single source of intelligence provide a complete picture. The art of multi-source integration, or fusion, is the process of combining information from different intelligence disciplines (such as HUMINT, SIGINT, and OSINT) to create a more comprehensive and reliable assessment.
            </p>

            <Card className="my-8">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold mb-4">The Value of Fusion</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-3 flex-shrink-0 mt-1" />
                    <span><strong>Corroboration:</strong> Multiple sources can confirm the validity of information.</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-3 flex-shrink-0 mt-1" />
                    <span><strong>Completeness:</strong> Each source can fill in gaps in the others, providing a more complete picture.</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-3 flex-shrink-0 mt-1" />
                    <span><strong>Confidence:</strong> A conclusion supported by multiple, independent sources is more likely to be accurate.</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-3 flex-shrink-0 mt-1" />
                    <span><strong>Deception Detection:</strong> Discrepancies between sources can be an indicator of deception.</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <h2>Conclusion</h2>
            <p>
              Multi-source integration is a fundamental skill for any intelligence analyst. It is a key part of the analytical process that transforms raw information into reliable, actionable intelligence.
            </p>
          </div>
        </div>

        <div className="lg:col-span-1">
          <div className="sticky top-8">
            <div className="bg-slate-50 rounded-lg p-6">
              <h3 className="font-semibold text-lg mb-4">Related Topics</h3>
              <ul className="space-y-3">
                <li>
                  <Link
                    href="/topics/intelligence-types"
                    className="text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    Intelligence Types
                  </Link>
                </li>
                <li>
                  <Link
                    href="/topics/analysis-competing-hypotheses"
                    className="text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    Analysis of Competing Hypotheses
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <WhatsNext
        title="Continue Your Learning"
        items={[
          {
            title: "Analysis of Competing Hypotheses",
            description: "Learn a structured analytical technique for evaluating multiple competing explanations.",
            href: "/topics/analysis-competing-hypotheses",
            icon: "FileText",
          },
          {
            title: "Intelligence Report Fundamentals",
            description: "Dive deeper into the core principles of effective intelligence reporting.",
            href: "/topics/intelligence-report-fundamentals",
            icon: "GraduationCap",
          },
        ]}
      />
    </div>
  )
}
