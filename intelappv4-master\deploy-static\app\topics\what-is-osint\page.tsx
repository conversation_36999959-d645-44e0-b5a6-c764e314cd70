const OSINTPage = () => {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-4">What is OSINT?</h1>
      <p className="text-gray-700 mb-4">
        <em>"Professional internet stalking with a badge and a really good understanding of privacy settings."</em>
      </p>
      <p className="text-gray-700 mb-4">
        OSINT, or Open Source Intelligence, is the practice of collecting and analyzing information that is publicly
        available. This information can come from a variety of sources, including:
      </p>
      <ul className="list-disc list-inside mb-4">
        <li>Websites</li>
        <li>Social media</li>
        <li>News articles</li>
        <li>Government reports</li>
        <li>Academic publications</li>
      </ul>
      <p className="text-gray-700 mb-4">
        OSINT is used by a wide range of organizations, including law enforcement, intelligence agencies, and
        businesses. It can be used for a variety of purposes, such as:
      </p>
      <ul className="list-disc list-inside mb-4">
        <li>Investigating crimes</li>
        <li>Gathering intelligence</li>
        <li>Conducting market research</li>
        <li>Assessing risks</li>
      </ul>

      <h2 className="text-2xl font-bold mt-8 mb-4">Key Components of OSINT</h2>
      <p className="text-gray-700 mb-4">
        <em>"Google-fu, social media archaeology, and the ability to find anyone's high school yearbook photo."</em>
      </p>
      <ul className="list-disc list-inside mb-4">
        <li>
          <b>Identification of Sources:</b> Knowing where to look for information.
        </li>
        <li>
          <b>Data Collection:</b> Gathering the information from identified sources.
        </li>
        <li>
          <b>Data Analysis:</b> Processing and understanding the collected information.
        </li>
        <li>
          <b>Dissemination:</b> Sharing the analyzed intelligence with stakeholders.
        </li>
      </ul>

      <h2 className="text-2xl font-bold mt-8 mb-4">OSINT in Practice</h2>
      <p className="text-gray-700 mb-4">
        <em>"Where 'I found it on the internet' becomes a legitimate intelligence source citation."</em>
      </p>
      <p className="text-gray-700 mb-4">
        OSINT techniques are applied in various real-world scenarios. Examples include:
      </p>
      <ul className="list-disc list-inside mb-4">
        <li>
          <b>Cybersecurity:</b> Identifying potential threats and vulnerabilities.
        </li>
        <li>
          <b>Journalism:</b> Investigating stories and verifying facts.
        </li>
        <li>
          <b>Business Intelligence:</b> Understanding competitors and market trends.
        </li>
      </ul>

      <h2 className="text-2xl font-bold mt-8 mb-4">Career in OSINT</h2>
      <p className="text-gray-700 mb-4">
        <em>"Professional digital detective with 47 browser tabs open and somehow knowing what each one is for."</em>
      </p>
      <p className="text-gray-700 mb-4">
        A career in OSINT can be rewarding for those who enjoy research and analysis. Potential roles include:
      </p>
      <ul className="list-disc list-inside mb-4">
        <li>Intelligence Analyst</li>
        <li>Security Analyst</li>
        <li>Investigative Journalist</li>
      </ul>

      <h2 className="text-2xl font-bold mt-8 mb-4">Advanced OSINT Concepts</h2>
      <p className="text-gray-700 mb-4">
        <em>
          "When basic Google searches aren't enough, and you need to dive into the digital rabbit hole with specialized
          tools."
        </em>
      </p>
      <p className="text-gray-700 mb-4">Beyond basic search techniques, advanced OSINT involves:</p>
      <ul className="list-disc list-inside mb-4">
        <li>Using specialized search engines and databases.</li>
        <li>Analyzing metadata and network traffic.</li>
        <li>Employing scripting and automation for data collection.</li>
      </ul>
    </div>
  )
}

export default OSINTPage
